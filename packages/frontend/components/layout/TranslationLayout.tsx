'use client';

import React, { useState, useRef } from 'react';
import { MangaCanvas, MangaCanvasRef } from '@/components/canvas/MangaCanvas';
import { CanvasControls } from '@/components/canvas/CanvasControls';
import { TextRegionEditor } from '@/components/canvas/TextRegionEditor';
import { TextRegionList } from '@/components/canvas/TextRegionList';
import { TextRegionManager } from '@/components/canvas/TextRegionManager';
import { OCRProcessor } from '@/components/ocr/OCRProcessor';
import { TranslationProcessor } from '@/components/translation/TranslationProcessor';
import { CanvasToolbar } from '@/components/canvas/CanvasToolbar';
import { CanvasNavigationControls } from '@/components/canvas/CanvasNavigationControls';
import { CanvasMinimap } from '@/components/canvas/CanvasMinimap';
import { CanvasGridRulers } from '@/components/canvas/CanvasGridRulers';
import { useCanvas } from '@/hooks/useCanvas';
import { useAppContext } from '@/store/AppContext';
import {
  CanvasState,
  CanvasTool,
  CanvasTextRegion,
  TranslationUIState
} from '@/types/canvas';
import { ProjectResponse, ProjectPageResponse, TextRegionType, TranslationStatus } from '@/types/api';

interface TranslationLayoutProps {
  currentProject?: ProjectResponse | null;
  currentPage?: ProjectPageResponse | null;
  imageUrl?: string;
  onProjectChange?: (project: ProjectResponse) => void;
  onPageChange?: (pageId: string) => void;
  className?: string;
}

export const TranslationLayout: React.FC<TranslationLayoutProps> = ({
  currentProject: propCurrentProject,
  currentPage: propCurrentPage,
  imageUrl: propImageUrl,
  onProjectChange,
  onPageChange,
  className = ''
}) => {
  const canvasRef = useRef<MangaCanvasRef>(null);
  const { state, dispatch } = useAppContext();

  // Use AppContext state or fallback to props for backward compatibility
  const currentProject = state.currentProject || propCurrentProject;
  const currentPage = state.currentPage || propCurrentPage;
  const imageUrl = propImageUrl || (currentPage?.file_path ?
    `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${currentPage.file_path}` :
    undefined
  );

  const [selectedRegionId, setSelectedRegionId] = useState<string | undefined>();

  // Canvas hook for managing canvas state and operations
  const {
    canvasState,
    imageInfo,
    textRegions,
    initializeCanvas,
    updateCanvasState,
    addTextRegion,
    removeTextRegion,
    updateTextRegion,
    setTextRegions,
    zoomIn,
    zoomOut,
    resetZoom,
    setTool
  } = useCanvas({
    onRegionCreate: handleRegionCreate,
    onRegionUpdate: handleRegionUpdate,
    onRegionDelete: handleRegionDelete,
    onRegionSelect: handleRegionSelect
  });

  // Handle canvas state changes
  const handleCanvasStateChange = (canvasState: CanvasState) => {
    updateCanvasState(canvasState);
    dispatch({ type: 'UPDATE_CANVAS_STATE', payload: canvasState });
  };

  // Handle text region operations
  function handleRegionCreate(region: Partial<CanvasTextRegion>) {
    const newRegion: CanvasTextRegion = {
      id: `region-${Date.now()}`,
      page_id: currentPage?.id || '',
      region_type: region.region_type || TextRegionType.SPEECH_BUBBLE,
      x: region.x || 0,
      y: region.y || 0,
      width: region.width || 0.1,
      height: region.height || 0.1,
      original_text: region.original_text,
      confidence_score: region.confidence_score,
      translated_text: region.translated_text,
      translation_status: TranslationStatus.PENDING,
      font_family: region.font_family,
      font_size: region.font_size,
      font_color: region.font_color,
      background_color: region.background_color,
      isSelected: false,
      isEditing: false,
      borderColor: region.borderColor || '#3b82f6',
      fillOpacity: region.fillOpacity || 0.2,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    setTextRegions(prev => [...prev, newRegion]);
    setSelectedRegionId(newRegion.id);
  }

  function handleRegionUpdate(regionId: string, updates: Partial<CanvasTextRegion>) {
    setTextRegions(prev => prev.map(region =>
      region.id === regionId
        ? { ...region, ...updates, updated_at: new Date().toISOString() }
        : region
    ));
  }

  function handleRegionDelete(regionId: string) {
    setTextRegions(prev => prev.filter(region => region.id !== regionId));
    if (selectedRegionId === regionId) {
      setSelectedRegionId(undefined);
    }
  }

  function handleRegionSelect(regionId: string) {
    setSelectedRegionId(regionId);
    setTextRegions(prev => prev.map(region => ({
      ...region,
      isSelected: region.id === regionId
    })));
  }

  // Panel toggle functions
  const togglePanel = (panel: 'showProjectPanel' | 'showTextEditPanel' | 'showOCRPanel' | 'showTranslationPanel') => {
    dispatch({
      type: 'SET_PANEL_VISIBILITY',
      payload: { panel, visible: !state.ui[panel] }
    });
  };

  // Get selected region
  const selectedRegion = state.textRegions.find(region => region.id === selectedRegionId);

  return (
    <div className={`flex h-screen bg-gray-50 ${className}`}>
      {/* Left Sidebar - Project Navigation */}
      {state.ui.showProjectPanel && (
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Projects</h2>
              <button
                onClick={() => togglePanel('showProjectPanel')}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
                title="Hide project panel"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Project Info */}
          {currentProject && (
            <div className="p-4 border-b border-gray-200 bg-blue-50">
              <h3 className="font-medium text-gray-900 mb-1">Current Project</h3>
              <p className="text-sm text-gray-600">{currentProject.name}</p>
              <div className="flex items-center mt-2 text-xs text-gray-500">
                <span className="mr-4">{currentProject.source_language} → {currentProject.target_language}</span>
                {currentPage && <span>{currentPage.text_region_count} regions</span>}
              </div>
              {currentPage && (
                <div className="mt-2 text-xs text-gray-500">
                  <span>Page {currentPage.page_number} - {currentPage.original_filename}</span>
                </div>
              )}
            </div>
          )}

          {/* Text Regions List */}
          <div className="flex-1 overflow-hidden">
            <TextRegionList
              textRegions={state.textRegions}
              selectedRegionId={selectedRegionId}
              onRegionSelect={handleRegionSelect}
              onRegionUpdate={handleRegionUpdate}
              onRegionDelete={handleRegionDelete}
              className="h-full border-0 rounded-none shadow-none"
            />
          </div>
        </div>
      )}

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Toolbar */}
        <div className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            {/* Left side - Canvas controls */}
            <div className="flex items-center space-x-4">
              {!state.ui.showProjectPanel && (
                <button
                  onClick={() => togglePanel('showProjectPanel')}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
                  title="Show project panel"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
              )}

              <CanvasToolbar
                canvasState={state.canvas}
                onZoomIn={zoomIn}
                onZoomOut={zoomOut}
                onResetZoom={resetZoom}
                onFitToScreen={() => {
                  // Implement fit to screen functionality
                  resetZoom();
                }}
                onToolChange={setTool}
                onUndo={() => {
                  // TODO: Implement undo functionality
                  console.log('Undo');
                }}
                onRedo={() => {
                  // TODO: Implement redo functionality
                  console.log('Redo');
                }}
                onSave={() => {
                  // TODO: Implement save functionality
                  console.log('Save');
                }}
                onExport={() => {
                  // TODO: Implement export functionality
                  console.log('Export');
                }}
                canUndo={false}
                canRedo={false}
                className="border-0 shadow-none bg-transparent p-0"
              />
            </div>

            {/* Right side - Panel toggles */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => togglePanel('showOCRPanel')}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${state.ui.showOCRPanel
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
              >
                OCR
              </button>
              <button
                onClick={() => togglePanel('showTranslationPanel')}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${state.ui.showTranslationPanel
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
              >
                Translation
              </button>
              <button
                onClick={() => togglePanel('showTextEditPanel')}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${state.ui.showTextEditPanel
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
              >
                Edit
              </button>
            </div>
          </div>
        </div>

        {/* Canvas Area */}
        <div className="flex-1 flex">
          {/* Canvas Container */}
          <div className="flex-1 p-4 flex items-center justify-center relative">
            <div className="relative">
              {/* Canvas Grid and Rulers */}
              <CanvasGridRulers
                canvasState={state.canvas}
                imageInfo={imageInfo}
                showGrid={state.ui.showGrid}
                showRulers={state.ui.showRulers}
                className="absolute inset-0 pointer-events-none"
              />

              <MangaCanvas
                ref={canvasRef}
                imageUrl={imageUrl}
                width={800}
                height={600}
                onCanvasReady={initializeCanvas}
                onStateChange={handleCanvasStateChange}
                eventHandlers={{
                  onRegionCreate: handleRegionCreate,
                  onRegionUpdate: handleRegionUpdate,
                  onRegionDelete: handleRegionDelete,
                  onRegionSelect: handleRegionSelect
                }}
                className="shadow-lg"
              />

              {/* Text Region Manager */}
              <TextRegionManager
                canvas={canvasRef.current?.getCanvas() || null}
                imageInfo={imageInfo}
                textRegions={state.textRegions}
                onRegionCreate={handleRegionCreate}
                onRegionUpdate={handleRegionUpdate}
                onRegionDelete={handleRegionDelete}
                onRegionSelect={handleRegionSelect}
                selectedRegionId={selectedRegionId}
              />
            </div>

            {/* Canvas Navigation Controls - Floating */}
            <div className="absolute bottom-4 left-4">
              <CanvasNavigationControls
                canvasState={state.canvas}
                onZoomIn={zoomIn}
                onZoomOut={zoomOut}
                onResetZoom={resetZoom}
                onFitToScreen={() => resetZoom()}
                onPan={(deltaX, deltaY) => {
                  // TODO: Implement pan functionality
                  console.log('Pan:', deltaX, deltaY);
                }}
                onToolChange={setTool}
                onToggleGrid={() => {
                  dispatch({
                    type: 'SET_PANEL_VISIBILITY',
                    payload: { panel: 'showGrid' as any, visible: !state.ui.showGrid }
                  });
                }}
                onToggleRulers={() => {
                  dispatch({
                    type: 'SET_PANEL_VISIBILITY',
                    payload: { panel: 'showRulers' as any, visible: !state.ui.showRulers }
                  });
                }}
                showGrid={state.ui.showGrid}
                showRulers={state.ui.showRulers}
                className="bg-white rounded-lg shadow-lg"
              />
            </div>

            {/* Canvas Minimap - Floating */}
            <div className="absolute bottom-4 right-4">
              <CanvasMinimap
                canvasState={state.canvas}
                imageInfo={imageInfo}
                textRegions={state.textRegions}
                onViewportChange={(x, y) => {
                  // TODO: Implement viewport change
                  console.log('Viewport change:', x, y);
                }}
                width={200}
                height={150}
                className="bg-white rounded-lg shadow-lg border border-gray-200"
              />
            </div>
          </div>

          {/* Right Sidebar - Text Editor */}
          {state.ui.showTextEditPanel && (
            <div className="w-80 bg-white border-l border-gray-200">
              <TextRegionEditor
                region={selectedRegion || null}
                onUpdate={handleRegionUpdate}
                onDelete={handleRegionDelete}
                onClose={() => setSelectedRegionId(undefined)}
                className="h-full border-0 rounded-none shadow-none"
              />
            </div>
          )}
        </div>
      </div>

      {/* OCR Panel (Modal/Overlay) */}
      {state.ui.showOCRPanel && currentPage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <OCRProcessor
              pageId={currentPage.id}
              onOCRComplete={(regions) => {
                // Convert OCR results to canvas text regions and add them
                dispatch({ type: 'SET_TEXT_REGIONS', payload: regions });
                togglePanel('showOCRPanel');
              }}
              onClose={() => togglePanel('showOCRPanel')}
              className="h-full"
            />
          </div>
        </div>
      )}

      {/* Translation Panel (Modal/Overlay) */}
      {state.ui.showTranslationPanel && currentProject && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <TranslationProcessor
              textRegions={state.textRegions}
              selectedRegionIds={selectedRegionId ? [selectedRegionId] : []}
              sourceLanguage={currentProject.source_language}
              targetLanguage={currentProject.target_language}
              onTranslationComplete={(regionId, translatedText) => {
                // Update the specific text region with the translation
                dispatch({
                  type: 'UPDATE_TEXT_REGION',
                  payload: { id: regionId, updates: { translated_text: translatedText } }
                });
              }}
              onClose={() => togglePanel('showTranslationPanel')}
              className="h-full"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default TranslationLayout;
